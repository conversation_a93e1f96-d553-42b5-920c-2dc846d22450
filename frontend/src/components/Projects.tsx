import React, {useState, useEffect} from 'react';
import {
    Plus,
    Search,
    Edit,
    Trash2,
    Calendar,
    FileText,
    Package,
    Clock,
    Grid3X3,
    LayoutGrid,
    Columns,
    Settings
} from 'lucide-react';
import {apiClient} from '../api';
import type {Project, ProjectFormData} from '../types';

const Projects: React.FC = () => {
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingProject, setEditingProject] = useState<Project | null>(null);
    const [formData, setFormData] = useState<ProjectFormData>({
        name: '',
        description: '',
        config_file: null
    });
    const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
    const [layoutMode, setLayoutMode] = useState<'default' | 'wide' | 'narrow' | 'auto'>('auto');

    useEffect(() => {
        loadProjects();
    }, []);

    const loadProjects = async () => {
        try {
            setLoading(true);
            const data = await apiClient.getProjects();
            setProjects(data);
        } catch (error) {
            console.error('Failed to load projects:', error);
            setMessage({type: 'error', text: '加载项目失败'});
        } finally {
            setLoading(false);
        }
    };

    const handleCreateProject = async (e: React.FormEvent) => {
        e.preventDefault();
        try {
            await apiClient.createProject(formData);
            setMessage({type: 'success', text: '项目创建成功'});
            setShowCreateModal(false);
            setFormData({name: '', description: '', config_file: null});
            loadProjects();
        } catch (error) {
            setMessage({type: 'error', text: error instanceof Error ? error.message : '项目创建失败'});
        }
    };

    const handleEditProject = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!editingProject) return;

        try {
            await apiClient.updateProject(editingProject.id, {
                name: formData.name,
                description: formData.description
            });
            setMessage({type: 'success', text: '项目更新成功'});
            setShowEditModal(false);
            setEditingProject(null);
            setFormData({name: '', description: '', config_file: null});
            loadProjects();
        } catch (error) {
            setMessage({type: 'error', text: error instanceof Error ? error.message : '项目更新失败'});
        }
    };

    const handleDeleteProject = async (project: Project) => {
        if (!confirm(`确定要删除项目 "${project.name}" 吗？此操作不可撤销。`)) {
            return;
        }

        try {
            await apiClient.deleteProject(project.id);
            setMessage({type: 'success', text: '项目删除成功'});
            loadProjects();
        } catch (error) {
            setMessage({type: 'error', text: error instanceof Error ? error.message : '项目删除失败'});
        }
    };

    const openEditModal = (project: Project) => {
        setEditingProject(project);
        setFormData({
            name: project.name,
            description: project.description || '',
            config_file: null
        });
        setShowEditModal(true);
    };

    const filteredProjects = projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-CN');
    };

    // 获取网格布局类名
    const getGridClassName = () => {
        switch (layoutMode) {
            case 'wide':
                return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3';
            case 'default':
                return 'project-cards-grid-auto';
            case 'narrow':
                return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5';
            case 'auto':
                return 'project-cards-grid-auto';
            default:
                return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
        }
    };

    // 获取网格样式
    const getGridStyle = () => {
        if (layoutMode === 'auto') {
            return {
                '--card-min-width': '380px',
                '--card-max-width': '800px'
            };
        }
        return {};
    };

    return (
        <div className="p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">个人项目</h1>
                <p className="text-gray-600">管理您的个人项目和配置文件</p>
            </div>

            {/* 消息提示 */}
            {message && (
                <div className={`mb-6 p-4 rounded-md ${
                    message.type === 'success'
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : 'bg-red-50 text-red-800 border border-red-200'
                }`}>
                    {message.text}
                </div>
            )}

            {/* 工具栏 */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-1">
                    {/* 搜索框 */}
                    <div className="relative flex-1 max-w-md">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"/>
                        <input
                            type="text"
                            placeholder="搜索项目..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="project-search-input w-full pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    {/* 布局选择器 */}
                    <div className="flex items-center space-x-2">
                        <Settings className="h-4 w-4 text-gray-500"/>
                        <select
                            value={layoutMode}
                            onChange={(e) => setLayoutMode(e.target.value as 'default' | 'wide' | 'narrow' | 'auto')}
                            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                        >
                            <option value="wide">宽卡片布局</option>
                            <option value="narrow">窄卡片布局</option>
                            <option value="auto">自适应布局</option>

                        </select>
                    </div>
                </div>

                <button
                    onClick={() => setShowCreateModal(true)}
                    className="project-create-button flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                    <Plus className="h-4 w-4"/>
                    <span>创建项目</span>
                </button>
            </div>

            {/* 项目列表 */}
            {loading ? (
                <div className="text-center py-12">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-gray-600">加载中...</p>
                </div>
            ) : filteredProjects.length === 0 ? (
                <div className="text-center py-12">
                    <Package className="mx-auto h-12 w-12 text-gray-400"/>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">暂无项目</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        {searchTerm ? '没有找到匹配的项目' : '开始创建您的第一个项目'}
                    </p>
                    {!searchTerm && (
                        <div className="mt-6">
                            <button
                                onClick={() => setShowCreateModal(true)}
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                                <Plus className="h-4 w-4 mr-2"/>
                                创建项目
                            </button>
                        </div>
                    )}
                </div>
            ) : (
                <div className={`${getGridClassName()} gap-6`}
                     style={getGridStyle() as React.CSSProperties}>
                    {filteredProjects.map((project) => (
                        <div
                            key={project.id}
                            className="rounded-xl shadow-sm border border-gray-200/30 overflow-hidden project-card-hover group project-card-full-gradient project-card-container"
                        >
                            {/* 装饰性几何图形 - 全卡片背景装饰 */}
                            <div className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 rounded-full blur-sm"></div>
                            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-white/5 rounded-full blur-sm"></div>
                            <div
                                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>

                            {/* 统一的深色半透明覆盖层 - 占满100%宽高 */}
                            <div className="project-card-unified-overlay">
                                {/* 项目标题和操作按钮 */}
                                <div className="flex justify-between items-start mb-4">
                                    <h3 className="project-card-title font-semibold text-lg leading-tight break-words flex-1 mr-4">
                                        {project.name}
                                    </h3>

                                    {/* 操作按钮 */}
                                    <div className="flex space-x-1 flex-shrink-0">
                                        <button
                                            onClick={() => openEditModal(project)}
                                            className="project-card-action-button p-2 bg-white/20 text-white rounded-lg hover:bg-white/30 backdrop-blur-sm transition-all duration-200 hover:scale-110"
                                            title="编辑项目"
                                        >
                                            <Edit className="h-3.5 w-3.5"/>
                                        </button>
                                        <button
                                            onClick={() => handleDeleteProject(project)}
                                            className="project-card-action-button p-2 bg-white/20 text-white rounded-lg hover:bg-red-500/80 backdrop-blur-sm transition-all duration-200 hover:scale-110"
                                            title="删除项目"
                                        >
                                            <Trash2 className="h-3.5 w-3.5"/>
                                        </button>
                                    </div>
                                </div>

                                {/* 项目描述区域 - 固定高度确保一致性 */}
                                <div className="project-card-description-area">
                                    {project.description ? (
                                        <p className="project-card-text-unified text-sm leading-relaxed line-clamp-3 project-card-description">
                                            {project.description}
                                        </p>
                                    ) : (
                                        <p className="project-card-text-unified text-sm leading-relaxed opacity-60 italic">
                                            暂无项目描述
                                        </p>
                                    )}
                                </div>

                                {/* 项目信息区域 - 固定位置 */}
                                <div className="project-card-info-area">
                                    <div className="space-y-2.5 text-xs project-card-text-unified">
                                        <div className="flex items-center space-x-2">
                                            <Calendar className="h-3.5 w-3.5 text-blue-300"/>
                                            <span className="font-medium">创建：</span>
                                            <span>{formatDate(project.created_at)}</span>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Clock className="h-3.5 w-3.5 text-green-300"/>
                                            <span className="font-medium">更新：</span>
                                            <span>{formatDate(project.updated_at)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* 创建项目模态框 */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="project-modal relative top-20 mx-auto p-5 w-96 bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">创建新项目</h3>
                            <form onSubmit={handleCreateProject} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        项目名称 *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                                        className="project-modal-input w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        项目描述
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
                                        rows={3}
                                        className="project-modal-input w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        配置文件 (可选)
                                    </label>
                                    <input
                                        type="file"
                                        accept=".json,.yaml,.yml,.toml,.ini,.conf"
                                        onChange={(e) => setFormData(prev => ({
                                            ...prev,
                                            config_file: e.target.files?.[0] || null
                                        }))}
                                        className="project-modal-input w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        支持 JSON, YAML, TOML, INI, CONF 格式
                                    </p>
                                </div>
                                <div className="flex space-x-3 pt-4">
                                    <button
                                        type="submit"
                                        className="project-modal-button-primary flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        创建
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowCreateModal(false);
                                            setFormData({name: '', description: '', config_file: null});
                                        }}
                                        className="project-modal-button-secondary flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                                    >
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* 编辑项目模态框 */}
            {showEditModal && editingProject && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="project-modal relative top-20 mx-auto p-5 w-96 bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">编辑项目</h3>
                            <form onSubmit={handleEditProject} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        项目名称 *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => setFormData(prev => ({...prev, name: e.target.value}))}
                                        className="project-modal-input w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        项目描述
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) => setFormData(prev => ({...prev, description: e.target.value}))}
                                        rows={3}
                                        className="project-modal-input w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>
                                <div className="flex space-x-3 pt-4">
                                    <button
                                        type="submit"
                                        className="project-modal-button-primary flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                    >
                                        保存
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowEditModal(false);
                                            setEditingProject(null);
                                            setFormData({name: '', description: '', config_file: null});
                                        }}
                                        className="project-modal-button-secondary flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                                    >
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Projects;
